import { defineConfig } from 'drizzle-kit';

// Allow local development without an env var by defaulting to a file DB.
const rawUrl = (process.env.DATABASE_URL ?? 'file:./local.db').trim();
const normalizedUrl = rawUrl.includes(':')
  ? rawUrl
  : `file:${rawUrl.startsWith('./') || rawUrl.startsWith('/') ? rawUrl : `./${rawUrl}`}`;

// drizzle-kit >=0.21: use dialect "turso" for libSQL (including file: URLs)
export default defineConfig({
  schema: './src/lib/server/db/schema.ts',
  out: './drizzle',
  dialect: 'turso',
  dbCredentials: { url: normalizedUrl },
  verbose: true,
  strict: true
});
