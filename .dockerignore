# Dependencies
node_modules
npm-debug.log*
bun-debug.log*

# Build outputs
.svelte-kit
build
dist

# Environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Development files
.vscode
.idea
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Logs
logs
*.log

# Test coverage
coverage

# Temporary files
tmp
temp
