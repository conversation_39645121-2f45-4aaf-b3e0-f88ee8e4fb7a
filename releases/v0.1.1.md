# v0.1.1 — 2025-09-01

Polish, logging controls, and docs/build updates.

## Highlights

- Harden Clerk session verification with clearer status checks and graceful JSON parsing.
- Add structured, request-scoped logs; gate verbose logs behind `NODE_ENV !== 'production'`.
- Avoid importing the Svelte devtools plugin in production; lazy‑load in dev only.
- Docs updates: Bun-first workflow, Docker notes, Warp guide additions.

## Commits

- feat(hooks): harden Clerk session verification and add structured logging
- chore(hooks): gate hook logging behind NODE_ENV check to reduce prod noise
- build(vite): avoid importing devtools plugin in prod; lazy-load in dev only
- docs(readme): make Bun the primary workflow and add Docker notes
- docs(warp): add Bun quickstart, clarify npm/pnpm alternatives, and raw Docker commands

## Upgrade notes

- No breaking changes.
- Ensure `CLERK_SECRET_KEY` and `PUBLIC_CLERK_PUBLISHABLE_KEY` remain configured.
