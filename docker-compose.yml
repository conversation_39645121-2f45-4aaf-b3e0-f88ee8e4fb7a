version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vectrical-app
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - DATABASE_URL=file:/app/data/local.db
    volumes:
      - sqlite_data:/app/data
    restart: unless-stopped

  # Development service (optional)
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: base
    container_name: vectrical-dev
    ports:
      - '5173:5173'
    environment:
      - NODE_ENV=development
      - DATABASE_URL=file:/app/data/local.db
    volumes:
      - .:/app
      - sqlite_data:/app/data
      - /app/node_modules
    command: bun run dev --host 0.0.0.0
    restart: unless-stopped
    profiles:
      - dev

volumes:
  sqlite_data:
    driver: local
