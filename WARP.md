# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository. The repo is Bun‑first.

## Quickstart

```bash
# install dependencies
bun install

# run dev server
bun run dev

# run tests / lint / typecheck
bun run test
bun run lint
bun run check
```

Prefer npm/pnpm/yarn? All scripts are compatible — swap `bun run` for your tool.

## Project Overview

This is a SvelteKit 5 application with TypeScript, using:

- **Bun** as the JavaScript runtime and package manager
- **libSQL** database with Drizzle ORM
- **Tailwind CSS 4** for styling
- **Vitest** for testing with browser and server test environments
- **Docker** containerization support
- **ESLint & Prettier** for code quality
- **Clerk** for authentication (client SDK integrated; environment keys required)

## Common Development Commands

### Development Server

```bash
# Start development server (preferred with Bun)
bun run dev

# Start with browser auto-open
bun run dev -- --open
```

### Building and Preview

```bash
# Build for production
bun run build

# Preview production build locally
bun run preview
```

Tip: Production images and compose services also use Bun — see Docker section below.

### Database Operations

```bash
# Push schema changes to database (development)
bun run db:push

# Generate migration files
bun run db:generate

# Run migrations
bun run db:migrate

# Open Drizzle Studio (database GUI)
bun run db:studio
```

### Testing

```bash
# Run all tests once (non-watch)
bun run test

# Run tests in watch mode (client and server projects)
bun run test:unit

# Run an individual test file
bun run test:unit -- src/routes/page.svelte.spec.ts
```

### Code Quality

```bash
# Format code
bun run format

# Lint and check formatting
bun run lint

# Type checking
bun run check

# Type checking in watch mode
bun run check:watch
```

### Docker Operations

```bash
# Build Docker image
bun run docker:build

# Run production container (exposes port 3000)
bun run docker:up

# Run development container with hot reload (profile: dev, exposes port 5173)
bun run docker:up-dev

# Stop containers
bun run docker:down

# View logs
bun run docker:logs

# Clean up containers and images
bun run docker:clean
```

Equivalent raw Docker commands if you aren’t using package scripts:

```bash
# build and run prod-like container
docker-compose up --build app

# run dev profile with HMR
docker-compose --profile dev up app-dev
```

## Project Architecture

### Database Layer

- **ORM**: Drizzle ORM with libSQL
- **Schema**: Located in `src/lib/server/db/schema.ts`
- **Database client**: `src/lib/server/db/index.ts` (uses `@libsql/client`)
- **Configuration**: `drizzle.config.ts` with `driver: 'libsql'` (requires `DATABASE_URL` environment variable`; throws if missing during CLI use)

### Application Structure

```
src/
├── lib/
│   ├── server/
│   │   └── db/           # Database schema and client
│   ├── assets/           # Static assets
│   └── index.ts          # Library exports
├── routes/
│   ├── +layout.svelte    # Root layout with global styles
│   └── +page.svelte      # Home page
└── app.html              # HTML template
```

### Testing Setup

- **Vitest** with dual environment support (configured in `vite.config.ts`):
  - **Browser tests**: Svelte component tests using Playwright
  - **Server tests**: Node.js environment for server-side logic
- Browser tests: `src/**/*.svelte.{test,spec}.{js,ts}`
- Server tests: `src/**/*.{test,spec}.{js,ts}` (excluding Svelte tests)
- Setup file for browser tests: `vitest-setup-client.ts`

### Environment Configuration

- Copy `.env.example` to `.env`
- Required variables:
  - `DATABASE_URL` — default `local.db` (libSQL compatible)
  - `PUBLIC_CLERK_PUBLISHABLE_KEY` — client-side key (must be prefixed with `PUBLIC_` for SvelteKit)
  - `CLERK_SECRET_KEY` — server-side key
- You can use local file paths or remote libSQL/Turso URLs for `DATABASE_URL`.
- Keep secrets out of version control. Use environment-specific `.env` files or a secrets manager.

### Docker Configuration

- **Multi-stage build** with Bun runtime
- **Development profile** (`app-dev`): Hot reload with volume mounting, serves on 5173
- **Production** (`app`): Slim image that runs `bun run preview` on port 3000
- **Volume**: `sqlite_data` for database persistence (mounted at `/app/data`)

## Key Files to Understand

- `svelte.config.js` — SvelteKit configuration with auto adapter
- `vite.config.ts` — Vite configuration with Tailwind CSS and Vitest projects
- `drizzle.config.ts` — Database schema management (libSQL driver)
- `docker-compose.yml` — Multi-service container setup (dev and prod services)
- `Dockerfile` — Multi-stage build with Bun and preview server
- `vitest-setup-client.ts` — Browser test setup
- `package.json` — Scripts and dependencies (Bun lock file)

## Development Notes

- This project uses **Svelte 5** with the new runes syntax (`$props`, `@render`)
- **Bun** is the preferred runtime (faster than npm/yarn)
- Database migrations should be generated and run in sequence
- Use `bun run db:studio` for visual database management
- Both development and production environments are containerized
- Tests run in both browser and Node.js environments for comprehensive coverage
