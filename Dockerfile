# Use Bun's official image as the base
FROM oven/bun:1 AS base

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json bun.lock* ./

# Install dependencies
RUN bun install --frozen-lockfile

# Copy source code
COPY . .

# Build stage
FROM base AS builder
RUN bun run build

# Production stage
FROM oven/bun:1-slim AS production

WORKDIR /app

# Copy built application, config files, and source structure
COPY --from=builder /app/.svelte-kit ./.svelte-kit
COPY --from=builder /app/package.json ./
COPY --from=builder /app/bun.lock* ./
COPY --from=builder /app/svelte.config.js ./
COPY --from=builder /app/vite.config.ts ./

# Install all dependencies (SvelteKit needs some dev dependencies at runtime)
RUN bun install --frozen-lockfile

# Create data directory for SQLite
RUN mkdir -p /app/data

# Expose the port the app runs on
EXPOSE 3000

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000
ENV HOST=0.0.0.0

# Start the application
CMD ["bun", "run", "preview", "--host", "0.0.0.0", "--port", "3000"]
