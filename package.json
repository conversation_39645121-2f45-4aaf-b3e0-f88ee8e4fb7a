{"name": "2025-08-26-vectrical", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test:unit": "vitest", "test": "npm run test:unit -- --run", "db:push": "drizzle-kit push", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "docker:build": "docker-compose build", "docker:up": "docker-compose up", "docker:up-dev": "docker-compose --profile dev up app-dev", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down -v --rmi all"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@internationalized/date": "^3.8.1", "@lucide/svelte": "^0.515.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@tailwindcss/vite": "^4.0.0", "@types/better-sqlite3": "^7.6.12", "@types/node": "^22", "@vitest/browser": "^3.2.3", "bits-ui": "^2.8.6", "clsx": "^2.1.1", "drizzle-kit": "^0.31.4", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "playwright": "^1.53.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.7", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^7.0.4", "vite-plugin-devtools-json": "^1.0.0", "vitest": "^3.2.3", "vitest-browser-svelte": "^0.1.0"}, "dependencies": {"@clerk/clerk-js": "^5.88.0", "@libsql/client": "^0.15.13", "drizzle-orm": "^0.40.0"}}