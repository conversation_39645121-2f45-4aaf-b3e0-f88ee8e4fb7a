
91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

91131 |         const accepted = ctx.req.header("Accept-Encoding");
91132 |         const encoding = (options == null ? void 0 : options.encoding) ?? ENCODING_TYPES.find((encoding2) => accepted == null ? void 0 : accepted.includes(encoding2));
91133 |         if (!encoding || !ctx.res.body) {
91134 |           return;
91135 |         }
91136 |         const stream = new CompressionStream(encoding);
                                   ^
ReferenceError: CompressionStream is not defined
      at compress2 (/Users/<USER>/dev/2025-08-26-Vectrical/node_modules/drizzle-kit/bin.cjs:91136:28)

^C
MacBook-Pro-3:2025-08-26-Vectrical hakan$ 