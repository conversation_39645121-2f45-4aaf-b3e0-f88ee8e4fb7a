# Changelog

All notable changes to this project will be documented in this file.

## [0.1.2] - 2025-09-01

UI foundation, shadcn‑svelte setup, and agent docs

- UI / Theme:
  - Add dark‑mode bootstrap script in `src/app.html` so the `dark` klass sätts innan render för att undvika FOUC.
  - Add `ThemeToggle` component (`src/lib/components/ui/theme-toggle.svelte`) och montera i root‑layouten.
  - Introduce tokeniserat tema i `src/app.css` (Tailwind v4) samt `tw-animate-css` import; definiera CSS‑variabler för färger/radius.

- shadcn‑svelte:
  - Add `components.json` konfiguration (alias till `$lib/components/ui`, `$lib/utils`, m.fl.).
  - Förbered CLI‑flöde för att lägga till komponenter via `bunx shadcn-svelte add ...`.

- Utils och beroenden:
  - Add `src/lib/utils.ts` med `cn` (clsx + tailwind-merge) och hjälptyper.
  - Add dev deps: `@lucide/svelte`, `clsx`, `tailwind-merge`, `tailwind-variants`, `tw-animate-css` i `package.json`.

- Dokumentation:
  - Add `AGENTS.md` med riktlinjer för AI‑agenter (stack, kommandon, auth/DB/test) och ny sektion för shadcn‑svelte (CLI, alias, Tailwind v4‑notiser, exempel).

## [0.1.1] - 2025-09-01

Polish, logging controls, and docs/build updates

- Hooks:
  - Harden Clerk session verification with clearer status checks and graceful JSON parsing.
  - Add structured, request-scoped logs; gate verbose logs behind `NODE_ENV !== 'production'`.

- Build:
  - Avoid importing the Svelte devtools plugin in production; lazy‑load in dev only.

- Docs:
  - README: make Bun the primary workflow and add Docker usage notes.
  - Warp guide: add Bun quickstart, npm/pnpm alternatives, and raw Docker commands.

## [0.1.0] - 2025-09-01

Authentication integration and hardening

- Client setup:
  - Added client-side Clerk initialization on `/` and `/app` using `@clerk/clerk-js` with dynamic imports.
  - On `/`: mounts Sign In and redirects to `/app` after auth. If already signed in, redirects immediately to `/app`.
  - On `/app`: loads Clerk with SvelteKit router (`routerPush`/`routerReplace`) and `standardBrowser: true`; mounts `UserButton`.

- Server protection:
  - Added server-side authentication guard in `src/hooks.server.ts`.
  - Verifies the `__session` cookie against Clerk backend (`POST https://api.clerk.com/v1/sessions/verify`) using `CLERK_SECRET_KEY`.
  - Protects all routes except the public `/`. Unauthenticated GETs redirect to `/`; non-GETs return `401`.
  - Exposes verified session on `event.locals.clerk.session` for future use.

- Proxy experiment (implemented, debugged, then removed):
  - Implemented a first-party `/clerk` proxy to avoid third‑party cookie issues.
  - Added cookie rewriting (Domain -> origin), `Accept-Encoding: identity`, body buffering to avoid Undici `duplex` errors, and upstream error logging.
  - Despite fixes, Clerk still returned `dev_browser_unauthenticated` in this environment; reverted to standard setup for simplicity and predictability.

- Environment & configs:
  - Confirmed `.env` usage with `PUBLIC_CLERK_PUBLISHABLE_KEY` and `CLERK_SECRET_KEY` (kept secrets out of VCS via `.gitignore`).
  - Added/verified `.env.example` entries for quick onboarding.

- QA & guidance:
  - Verified flows: sign-in on `/`, redirect to `/app`, persistent session on refresh, sign-out redirects back to `/`.
  - Noted requirement in development to allow third‑party cookies for `*.clerk.accounts.dev` so Clerk can set the dev‑browser cookie.

- Repo hygiene:
  - Initialized git repository, created initial commit bundling the above changes.
  - Renamed branch to `main`.

### Notes

- For production, consider local JWT verification via JWKS (e.g., `@clerk/backend`) to avoid per‑request network verification.
- If additional public routes are needed, whitelist them in `hooks.server.ts`.
