# sv

Everything you need to build a Svelte project, powered by [`sv`](https://github.com/sveltejs/cli).

## Creating a project

If you're seeing this, you've probably already done this step. Congrats!

```sh
# create a new project in the current directory
npx sv create

# create a new project in my-app
npx sv create my-app
```

## Developing (Bun-first)

This repo is set up to use [Bun](https://bun.sh) as the primary runtime and package manager.

```sh
# install deps
bun install

# start the dev server
bun run dev

# or open a browser tab automatically
bun run dev -- --open
```

If you prefer npm/pnpm/yarn, scripts are compatible — just replace `bun run` with your tool of choice.

## Building

Create and preview a production build with Bun:

```sh
bun run build
bun run preview
```

> To deploy your app, you may need to install an [adapter](https://svelte.dev/docs/kit/adapters) for your target environment.

## Docker

Container workflows also use Bun. See `Dockerfile` and `docker-compose.yml`.

Common commands:

```sh
# build and run prod-like container
docker-compose up --build app

# run dev profile (bind mounts, HMR)
docker-compose --profile dev up app-dev
```

For more commands (test, lint, DB tooling) see `WARP.md`.
