CREATE TABLE `object` (
	`id` text PRIMARY KEY NOT NULL,
	`project_id` text NOT NULL,
	`name` text NOT NULL,
	`type` text NOT NULL,
	`address` text,
	`status` text NOT NULL
);
--> statement-breakpoint
CREATE TABLE `project` (
	`id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`city` text NOT NULL,
	`status` text NOT NULL,
	`progress` integer DEFAULT 0,
	`objects_count` integer DEFAULT 0,
	`work_orders_count` integer DEFAULT 0
);
--> statement-breakpoint
CREATE TABLE `status` (
	`key` text PRIMARY KEY NOT NULL,
	`label` text NOT NULL,
	`color` text(16) NOT NULL,
	`order_` integer DEFAULT 0
);
--> statement-breakpoint
CREATE TABLE `user` (
	`id` integer PRIMARY KEY NOT NULL,
	`age` integer
);
--> statement-breakpoint
CREATE TABLE `work_order` (
	`id` text PRIMARY KEY NOT NULL,
	`object_id` text NOT NULL,
	`title` text NOT NULL,
	`status` text NOT NULL,
	`assignee` text
);
