# AGENTS.md

<PERSON><PERSON><PERSON><PERSON><PERSON> för AI‑agenter och verktyg (t.ex. <PERSON>LI, <PERSON><PERSON>, <PERSON><PERSON><PERSON>) som arbetar i detta repo.

## Snabb översikt

- Stack: SvelteKit 5 (<PERSON><PERSON><PERSON> runes), Type<PERSON>, <PERSON>un, Tailwind CSS 4, Drizzle ORM + lib<PERSON><PERSON>, <PERSON> (auth), <PERSON><PERSON><PERSON>, <PERSON><PERSON>.
- Primär runtime/PM: Bun. Alla `npm`/`pnpm`/`yarn` kommandon funkar, men föredra `bun`.
- Offentlig ruta: `/`. Ö<PERSON><PERSON>a rutter är skyddade i `src/hooks.server.ts` via Clerk-session.

## Körkommandon

```bash
# installera beroenden
bun install

# utvecklingsserver
bun run dev           # ev. "-- --open" för auto-öppning

# bygga och förhandsgranska
bun run build
bun run preview

# test / lint / typecheck
bun run test
bun run lint
bun run check

# databas (Drizzle + libSQL)
bun run db:push
bun run db:generate
bun run db:migrate
bun run db:studio

# docker
bun run docker:build
bun run docker:up
bun run docker:up-dev
bun run docker:down
bun run docker:logs
bun run docker:clean
```

## Miljövariabler

- `DATABASE_URL` (krävs av libSQL/Drizzle; filväg eller fjärr‑URL)
- `PUBLIC_CLERK_PUBLISHABLE_KEY` (publik, klient)
- `CLERK_SECRET_KEY` (server, används i `hooks.server.ts`)

Uppdatera `.env.example` om nya variabler tillkommer och checka inte in hemligheter.

## Projektstruktur (förenklad)

```
src/
├── app.html                 # Hanterar dark‑mode klass tidigt
├── routes/
│   ├── +layout.svelte       # Global CSS, favicon, tema‑toggle
│   ├── +page.svelte         # Clerk Sign‑In (publik)
│   └── app/+page.svelte     # Skyddad vy, Clerk UserButton
├── lib/
│   ├── server/db/           # Drizzle schema + klient
│   ├── components/ui/       # UI‑komponenter
│   └── index.ts             # Biblioteks‑exports
└── hooks.server.ts          # Rutt‑skydd, Clerk verifiering
```

## UI‑komponenter (shadcn‑svelte)

Detta repo är förberett för shadcn‑svelte:

- Konfiguration finns i `components.json:1` (alias pekar mot `$lib/...`).
- Ikoner: `@lucide/svelte` är installerat.
- CSS: Tailwind v4 i `src/app.css:1` med tema‑tokens och dark‑mode.

### CLI‑flöde

```bash
# (engångs) init om nödvändigt – vi har redan components.json
bunx shadcn-svelte@latest init -y

# lägg till komponenter
bunx shadcn-svelte@latest add button input card dialog dropdown-menu

# uppdatera en komponent (om CLI föreslår ändringar)
bunx shadcn-svelte@latest add button --overwrite
```

Komponenter placeras under `src/lib/components/ui/*`. Hjälpfunktioner (t.ex. `cn`) finns i `src/lib/utils.ts:1`.

### Användning

```svelte
<script lang="ts">
	import { Button } from '$lib/components/ui/button';
</script>

<Button variant="default">Spara</Button>
```

### Tailwind v4‑notiser

- Vi använder `@import "tailwindcss";` och teman i `src/app.css:1` i stället för en klassisk `tailwind.config.*`.
- Repo:t använder `tw-animate-css` (Tailwind v4‑kompatibel). Om en importerad komponent använder klasser från `tailwindcss-animate` (t.ex. `animate-in`, `fade-in-50`), verifiera animationerna och mappa dem vid behov till motsvarande `tw-animate-css` klasser.
- Följ tokens/util‑klasserna (t.ex. `bg-background`, `text-foreground`, `border-border`, `outline-ring/50`) för att behålla tematiken.

### Alias och exportmönster

- Alias i `components.json:1`:
  - `ui` → `$lib/components/ui`
  - `utils` → `$lib/utils`
- Exportera centrala byggstenar via `src/lib/index.ts:1` om du vill ha en samlad import‑yta.

## Kodkonventioner

- Format & lint: kör `bun run format` och `bun run lint` innan större ändringar.
- TypeScript överallt där möjligt.
- Svelte 5 runes: använd `$props`, `@render` m.m. (följ befintliga mönster i `+layout.svelte`).
- Tailwind CSS 4: håll klasser konsistenta, stöd för dark‑mode finns via `<html class="dark">` i `src/app.html`.
- Undvik att introducera nya byggverktyg/formatters. Håll förändringar minimala och fokuserade.
- Lägg inte till licenshuvuden om det inte efterfrågas.

## Autentisering (Clerk)

- Den publika startsidan `/` monterar Clerk Sign‑In dynamiskt (undvik SSR‑problem): se `src/routes/+page.svelte`.
- Skyddad appvy finns i `src/routes/app/+page.svelte` och förlitar sig på att `hooks.server.ts` blockerar oautentiserade anrop.
- Server‑hook (`src/hooks.server.ts`) verifierar sessionkaka `__session` mot Clerk om `CLERK_SECRET_KEY` är satt. GET omdirigeras till `/` vid saknad/ogiltig session; andra metoder får `401`.
- För klientkod: behåll mönstret med dynamisk import och konstruktorupplockning från `@clerk/clerk-js` (ESM/CJS‑interop).

## Databas (Drizzle + libSQL)

- Schema: `src/lib/server/db/schema.ts`. Klient: `src/lib/server/db/index.ts` (kastar om `DATABASE_URL` saknas).
- Migreringsflöde:
  1. Uppdatera schema i `schema.ts`
  2. `bun run db:generate` (generera migrations)
  3. `bun run db:migrate` (kör migrations)
  4. Vid ny DB eller utveckling: `bun run db:push`
- Anslutning sker via `@libsql/client`; lokalt kan `DATABASE_URL` peka på en fil (t.ex. `./data/local.db`).

## Testning

- Vitest är konfigurerat med både browser‑ och servermiljö (se `vite.config.ts`).
- Filnamnsmönster:
  - Browser/Svelte: `src/**/*.svelte.{test,spec}.{js,ts}`
  - Server/övrigt: `src/**/*.{test,spec}.{js,ts}` (exkl. Svelte‑tester)
- Init för browser‑tester: `vitest-setup-client.ts`.

## Vanliga playbooks

### 1) Lägg till skyddad sida

1. Skapa `src/routes/app/<namn>/+page.svelte`.
2. Lägg UI‑komponenter i `src/lib/components/...` vid behov och importera i sidan.
3. Förlita dig på `hooks.server.ts` för att blockera oinloggade besökare.

### 2) Lägg till API‑endpoint

Skapa `src/routes/api/example/+server.ts`:

```ts
// src/routes/api/example/+server.ts
import type { RequestHandler } from '@sveltejs/kit';

export const GET: RequestHandler = async ({ locals }) => {
	// locals.clerk?.session finns efter verifiering i hooks.server.ts
	if (!locals.clerk?.session) return new Response('Unauthorized', { status: 401 });
	return Response.json({ ok: true });
};
```

### 3) Använd databasen i en server‑route

```ts
// src/routes/api/users/+server.ts
import type { RequestHandler } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { user } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';

export const GET: RequestHandler = async () => {
	const rows = await db.select().from(user).where(eq(user.age, 42));
	return Response.json(rows);
};
```

### 4) Skapa och exponera en UI‑komponent

1. Lägg till fil i `src/lib/components/...`.
2. Exportera från `src/lib/index.ts` vid behov.
3. Skriv komponenttest i `src/**/*.svelte.spec.ts`.

## Kända fallgropar

- Clerk ESM/CJS‑interop: Följ befintligt dynamisk‑import‑mönster när du använder `@clerk/clerk-js` för att undvika SSR‑varningar.
- Missad `DATABASE_URL`: Drizzle‑CLI och klienten kräver den; lokalt kan en fil‑URL räcka.
- Route‑id för statiska resurser: `hooks.server.ts` släpper igenom rutter utan id (assets); ändra inte detta utan god anledning.
- Dark mode: `<html>`‑klassen togglas tidigt i `src/app.html`; håll detta intakt för att undvika FOUC.

## Kvalitetssäkring & ändringsdisciplin

- Kör: `bun run lint`, `bun run check`, `bun run test` innan leverans.
- Gör minimala, fokuserade ändringar; undvik att blanda orelaterade fixar.
- Uppdatera dokumentation vid behov (`README.md`, `WARP.md`, och denna fil).
- Vid nya miljövariabler: uppdatera `.env.example` och kod som konsumerar dem.

## För AI‑agenter (praktiskt arbetssätt)

- Läs kod selektivt (använd t.ex. `rg` för att hitta relevanta filer).
- Följ befintliga mönster och filplaceringar; ändra inte filstruktur i onödan.
- Välj Bun‑kommandon (snabbare), men håll skript kompatibla med npm‑ekosystemet.
- När du lägger till DB‑schema, skapa migrations och kör dem lokalt.
- Skriv tester för ny logik och kör alla tester lokalt.
- Stäm av med användaren vid oklarheter i krav/scope innan större ingrepp.

---

Behöver du fler riktlinjer eller ett nytt playbook‑avsnitt? Skapa ett förslag i `AGENTS.md` och uppdatera vid nästa ändring.
