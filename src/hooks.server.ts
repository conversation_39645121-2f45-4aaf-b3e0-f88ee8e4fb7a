import type { <PERSON>le } from '@sveltejs/kit';
import { redirect } from '@sveltejs/kit';

const CLERK_VERIFY_URL = 'https://api.clerk.com/v1/sessions/verify';

const debug = process.env.NODE_ENV !== 'production';

async function verifyClerkSession(token: string, secretKey: string) {
	if (debug) console.log(`[HOOKS] Attempting Clerk API call to verify session`);
	try {
		const res = await fetch(CLERK_VERIFY_URL, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${secretKey}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ token })
		});
		if (debug) console.log(`[HOOKS] Clerk API response status: ${res.status}`);
		if (!res.ok) return null;
		try {
			const result = await res.json();
			if (debug) console.log(`[HOOKS] Clerk session verification successful`);
			return result;
		} catch {
			if (debug) console.log(`[HOOKS] Failed to parse Clerk API response`);
			return null;
		}
	} catch (error) {
		if (debug) console.log(`[HOOKS] Network error during Clerk API call:`, error);
		return null;
	}
}

export const handle: Handle = async ({ event, resolve }) => {
	if (debug)
		console.log(`[HOOKS] Processing request: ${event.request.method} ${event.url.pathname}`);

	// Allow static/assets and the public sign-in page
	const { pathname } = event.url;

	// Assets don't have a route id
	if (event.route.id == null) {
		return resolve(event);
	}

	// Public routes
	if (pathname === '/') {
		return resolve(event);
	}

	const sessionCookie = event.cookies.get('__session');
	const secret = process.env.CLERK_SECRET_KEY;
	const devBypass = process.env.AUTH_DEV_BYPASS === 'true';

	// Dev bypass: if no Clerk secret configured OR explicit bypass flag, allow and set a fake session.
	if (!secret || devBypass) {
		if (debug)
			console.log(
				`[HOOKS] ${!secret ? 'No CLERK_SECRET_KEY' : 'AUTH_DEV_BYPASS=true'}; allowing request with dev session`
			);
		event.locals.clerk = { session: { dev: true } } as any;
		return resolve(event);
	}

	if (debug)
		console.log(
			`[HOOKS] Session cookie present: ${!!sessionCookie}, Secret key present: ${!!secret}`
		);

	// If no cookie, block
	if (!sessionCookie) {
		if (debug) console.log(`[HOOKS] No session cookie found, redirecting to /`);
		if (event.request.method === 'GET') {
			throw redirect(303, '/');
		}
		return new Response('Unauthorized', { status: 401 });
	}

	// Verify with Clerk if we have a secret
	if (secret) {
		if (debug) console.log(`[HOOKS] Verifying session with Clerk...`);
		const verified = await verifyClerkSession(sessionCookie, secret);
		if (!verified) {
			if (debug) {
				console.log(`[HOOKS] Session verification failed. Allowing in dev for local testing.`);
				// Allow in dev instead of redirecting to keep DX smooth
				event.locals.clerk = { session: { dev: true } } as any;
				return resolve(event);
			}
			if (event.request.method === 'GET') {
				throw redirect(303, '/');
			}
			return new Response('Unauthorized', { status: 401 });
		}
		if (debug) console.log(`[HOOKS] Session verification successful`);
		// Optionally expose basic identity in locals
		event.locals.clerk = {
			session: verified
		};
	}

	if (debug) console.log(`[HOOKS] Request processed successfully, resolving...`);
	return resolve(event);
};
