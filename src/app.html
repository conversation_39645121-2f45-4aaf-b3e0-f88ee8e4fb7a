<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<script>
			(function () {
				try {
					var stored = localStorage.getItem('theme');
					var prefers = window.matchMedia('(prefers-color-scheme: dark)').matches;
					var dark = stored ? stored === 'dark' : prefers;
					document.documentElement.classList.toggle('dark', dark);
				} catch (e) {
					// no-op
				}
			})();
		</script>
		%sveltekit.head%
	</head>
	<body data-sveltekit-preload-data="hover">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>
