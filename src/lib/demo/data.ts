export type StatusKey = 'planned' | 'in_progress' | 'blocked' | 'done';

export type Project = {
	id: string;
	name: string;
	city: string;
	status: StatusKey;
	objects: number;
	workOrders: number;
	progress: number; // 0-100
};

export type Obj = {
	id: string;
	projectId: string;
	name: string;
	type: 'Building' | 'Cabinet' | 'Street';
	address?: string;
	status: StatusKey;
};

export type WorkOrder = {
	id: string;
	objectId: string;
	title: string;
	status: StatusKey;
	assignee?: string;
};

export const statuses: Record<
	StatusKey,
	{ label: string; color: 'slate' | 'blue' | 'red' | 'green' | 'amber' | 'violet' }
> = {
	planned: { label: 'Planned', color: 'slate' },
	in_progress: { label: 'In progress', color: 'blue' },
	blocked: { label: 'Blocked', color: 'red' },
	done: { label: 'Done', color: 'green' }
};

export const projects: Project[] = [
	{
		id: 'p1',
		name: 'ACME Fiber North',
		city: 'Berlin',
		status: 'in_progress',
		objects: 42,
		workOrders: 128,
		progress: 62
	},
	{
		id: 'p2',
		name: 'Berlin FTTH Lot 3',
		city: 'Berlin',
		status: 'planned',
		objects: 18,
		workOrders: 31,
		progress: 12
	},
	{
		id: 'p3',
		name: 'Munich Cabinets',
		city: 'Munich',
		status: 'blocked',
		objects: 9,
		workOrders: 15,
		progress: 34
	}
];

export const objects: Obj[] = [
	{
		id: 'o1',
		projectId: 'p1',
		name: '12 Main St',
		type: 'Building',
		address: 'Main St 12',
		status: 'in_progress'
	},
	{ id: 'o2', projectId: 'p1', name: 'Cabinet #A-07', type: 'Cabinet', status: 'planned' },
	{ id: 'o3', projectId: 'p2', name: 'Lindenstrasse 4-8', type: 'Street', status: 'planned' },
	{ id: 'o4', projectId: 'p3', name: 'Cabinet #M-21', type: 'Cabinet', status: 'blocked' }
];

export const workOrders: WorkOrder[] = [
	{
		id: 'w1',
		objectId: 'o1',
		title: 'Blow fiber conduit',
		status: 'in_progress',
		assignee: 'Crew Alpha'
	},
	{ id: 'w2', objectId: 'o1', title: 'Pull cable', status: 'planned', assignee: 'Crew Beta' },
	{ id: 'w3', objectId: 'o2', title: 'Install cabinet base', status: 'blocked' },
	{ id: 'w4', objectId: 'o4', title: 'Terminate fibers', status: 'done', assignee: 'Crew Gamma' }
];
