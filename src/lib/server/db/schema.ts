import { sqliteTable, integer, text } from 'drizzle-orm/sqlite-core';

export const user = sqliteTable('user', {
	id: integer('id').primaryKey(),
	age: integer('age')
});

// Status definitions for projects/objects/work orders
// key: machine key (e.g., planned, in_progress)
// label: display label (editable by PM)
// color: token name to style UI; limited set
export const status = sqliteTable('status', {
	key: text('key').primaryKey().notNull(),
	label: text('label').notNull(),
	color: text('color', { length: 16 }).notNull(), // e.g., slate|blue|red|green|amber|violet
	order: integer('order_').default(0)
});

// Core domain tables
export const project = sqliteTable('project', {
	id: text('id').primaryKey().notNull(),
	name: text('name').notNull(),
	city: text('city').notNull(),
	status: text('status').notNull(), // references status.key (no FK for simplicity)
	progress: integer('progress').default(0),
	objectsCount: integer('objects_count').default(0),
	workOrdersCount: integer('work_orders_count').default(0)
});

export const object = sqliteTable('object', {
	id: text('id').primaryKey().notNull(),
	projectId: text('project_id').notNull(),
	name: text('name').notNull(),
	type: text('type').notNull(), // Building|Cabinet|Street
	address: text('address'),
	status: text('status').notNull()
});

export const workOrder = sqliteTable('work_order', {
	id: text('id').primaryKey().notNull(),
	objectId: text('object_id').notNull(),
	title: text('title').notNull(),
	status: text('status').notNull(),
	assignee: text('assignee')
});
