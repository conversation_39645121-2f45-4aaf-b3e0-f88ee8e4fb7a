import { db } from './index';
import { project, object, workOrder, status as statusTable } from './schema';
import { ensureSchema } from './bootstrap';

export async function ensureSeed() {
  await ensureSchema();
	const pr = await db.select().from(project).limit(1);
	if (pr.length === 0) {
		await db.insert(project).values([
			{
				id: 'p1',
				name: 'ACME Fiber North',
				city: 'Berlin',
				status: 'in_progress',
				progress: 62,
				objectsCount: 2,
				workOrdersCount: 2
			},
			{
				id: 'p2',
				name: 'Berlin FTTH Lot 3',
				city: 'Berlin',
				status: 'planned',
				progress: 12,
				objectsCount: 1,
				workOrdersCount: 1
			},
			{
				id: 'p3',
				name: 'Munich Cabinets',
				city: 'Munich',
				status: 'blocked',
				progress: 34,
				objectsCount: 1,
				workOrdersCount: 1
			}
		]);
	}

	const ob = await db.select().from(object).limit(1);
	if (ob.length === 0) {
		await db.insert(object).values([
			{
				id: 'o1',
				projectId: 'p1',
				name: '12 Main St',
				type: 'Building',
				address: 'Main St 12',
				status: 'in_progress'
			},
			{
				id: 'o2',
				projectId: 'p1',
				name: 'Cabinet #A-07',
				type: 'Cabinet',
				address: null as any,
				status: 'planned'
			},
			{
				id: 'o3',
				projectId: 'p2',
				name: 'Lindenstrasse 4-8',
				type: 'Street',
				address: null as any,
				status: 'planned'
			},
			{
				id: 'o4',
				projectId: 'p3',
				name: 'Cabinet #M-21',
				type: 'Cabinet',
				address: null as any,
				status: 'blocked'
			}
		]);
	}

	const wo = await db.select().from(workOrder).limit(1);
	if (wo.length === 0) {
		await db.insert(workOrder).values([
			{
				id: 'w1',
				objectId: 'o1',
				title: 'Blow fiber conduit',
				status: 'in_progress',
				assignee: 'Crew Alpha'
			},
			{ id: 'w2', objectId: 'o1', title: 'Pull cable', status: 'planned', assignee: 'Crew Beta' },
			{
				id: 'w3',
				objectId: 'o2',
				title: 'Install cabinet base',
				status: 'blocked',
				assignee: null as any
			},
			{
				id: 'w4',
				objectId: 'o4',
				title: 'Terminate fibers',
				status: 'done',
				assignee: 'Crew Gamma'
			}
		]);
	}

	const st = await db
		.select()
		.from(statusTable)
		.limit(1)
		.catch(() => [] as any);
	if (!st || st.length === 0) {
		try {
			await db.insert(statusTable).values([
				{ key: 'planned', label: 'Planned', color: 'slate', order: 0 },
				{ key: 'in_progress', label: 'In progress', color: 'blue', order: 1 },
				{ key: 'blocked', label: 'Blocked', color: 'red', order: 2 },
				{ key: 'done', label: 'Done', color: 'green', order: 3 }
			]);
		} catch {}
	}
}
