import { client } from './index';

const statements = [
  `CREATE TABLE IF NOT EXISTS status (
    key TEXT PRIMARY KEY NOT NULL,
    label TEXT NOT NULL,
    color TEXT NOT NULL,
    order_ INTEGER DEFAULT 0
  )`,
  `CREATE TABLE IF NOT EXISTS project (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    city TEXT NOT NULL,
    status TEXT NOT NULL,
    progress INTEGER DEFAULT 0,
    objects_count INTEGER DEFAULT 0,
    work_orders_count INTEGER DEFAULT 0
  )`,
  `CREATE TABLE IF NOT EXISTS object (
    id TEXT PRIMARY KEY NOT NULL,
    project_id TEXT NOT NULL,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    address TEXT,
    status TEXT NOT NULL
  )`,
  `CREATE TABLE IF NOT EXISTS work_order (
    id TEXT PRIMARY KEY NOT NULL,
    object_id TEXT NOT NULL,
    title TEXT NOT NULL,
    status TEXT NOT NULL,
    assignee TEXT
  )`
];

export async function ensureSchema() {
  for (const sql of statements) {
    await client.execute(sql);
  }
}

