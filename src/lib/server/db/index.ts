import { drizzle } from 'drizzle-orm/libsql';
import { createClient } from '@libsql/client';
import * as schema from './schema';
import { env } from '$env/dynamic/private';

// Normalize DATABASE_URL to a valid libsql URL.
function normalizeUrl(input: string | undefined | null): string {
  const raw = (input ?? '').trim();
  if (!raw) return 'file:./local.db';
  if (raw.startsWith('file:') || raw.startsWith('libsql://') || raw.startsWith('http://') || raw.startsWith('https://')) {
    return raw;
  }
  const path = raw.startsWith('./') || raw.startsWith('/') ? raw : `./${raw}`;
  return `file:${path}`;
}

export const client = createClient({ url: normalizeUrl(env.DATABASE_URL) });

export const db = drizzle(client, { schema });
