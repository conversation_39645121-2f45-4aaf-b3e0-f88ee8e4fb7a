<script lang="ts">
	import { onMount } from 'svelte';
	import { Moon, Sun } from '@lucide/svelte';

	let isDark = false;

	function apply(dark: boolean) {
		document.documentElement.classList.toggle('dark', dark);
		try {
			localStorage.setItem('theme', dark ? 'dark' : 'light');
		} catch {}
		isDark = dark;
	}

	function toggle() {
		apply(!isDark);
	}

	onMount(() => {
		// Sync initial state with document / storage / system
		try {
			const stored = localStorage.getItem('theme');
			const prefers = window.matchMedia('(prefers-color-scheme: dark)');
			const current = document.documentElement.classList.contains('dark');
			isDark = stored ? stored === 'dark' : current;

			// React to system changes if user hasn't explicitly chosen
			const handle = (e: MediaQueryListEvent) => {
				const explicit = localStorage.getItem('theme');
				if (!explicit) apply(e.matches);
			};
			prefers.addEventListener?.('change', handle);
			return () => prefers.removeEventListener?.('change', handle);
		} catch {
			// noop
		}
	});
</script>

<button
	type="button"
	on:click={toggle}
	class="inline-flex items-center justify-center rounded-md border border-input bg-background px-2.5 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50"
	aria-label={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
	title={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
>
	{#if isDark}
		<Sun class="h-4 w-4" />
	{:else}
		<Moon class="h-4 w-4" />
	{/if}
</button>
