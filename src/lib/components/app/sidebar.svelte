<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Separator } from '$lib/components/ui/separator';
	import { page } from '$app/stores';
	import { House, Folder, Wrench, Settings, Boxes } from '@lucide/svelte';

	const nav = [
		{ label: 'Projects', href: '/app/projects', icon: Folder },
		{ label: 'Objects', href: '/app/objects', icon: Boxes },
		{ label: 'Work Orders', href: '/app/work-orders', icon: Wrench }
	];

	const manage = [{ label: 'Management', href: '/app/management', icon: Settings }];
</script>

<div class="flex h-full min-h-[calc(100dvh-56px)] flex-col">
	<div class="px-3 py-2">
		<a
			class="flex items-center gap-2 rounded-md px-2 py-2 text-sm text-muted-foreground hover:text-foreground"
			href="/app"
		>
			<House class="size-4" /> Home
		</a>
	</div>

	<Separator />

	<nav class="flex flex-1 flex-col gap-1 p-2">
		{#each nav as item}
			{@const Active = item.icon}
			<a
				href={item.href}
				aria-current={$page.url.pathname.startsWith(item.href) ? 'page' : undefined}
				class="flex items-center gap-2 rounded-md px-2 py-2 text-sm text-muted-foreground hover:bg-muted aria-[current=page]:bg-muted aria-[current=page]:text-foreground"
			>
				<Active class="size-4" />
				<span>{item.label}</span>
			</a>
		{/each}

		<div class="pt-2">
			<div class="px-2 pb-1 text-[0.7rem] tracking-wide text-muted-foreground uppercase">
				Management
			</div>
			{#each manage as item}
				{@const Active = item.icon}
				<a
					href={item.href}
					aria-current={$page.url.pathname.startsWith(item.href) ? 'page' : undefined}
					class="flex items-center gap-2 rounded-md px-2 py-2 text-sm text-muted-foreground hover:bg-muted aria-[current=page]:bg-muted aria-[current=page]:text-foreground"
				>
					<Active class="size-4" />
					<span>{item.label}</span>
				</a>
			{/each}
		</div>
	</nav>

	<div class="p-2">
		<Button href="/app/work-orders" variant="secondary" class="w-full">New Work Order</Button>
	</div>
</div>
