<script lang="ts">
	import { buttonVariants } from '$lib/components/ui/button';
	import { ChevronDown, Briefcase } from '@lucide/svelte';
	import {
		DropdownMenu,
		DropdownMenuContent,
		DropdownMenuItem,
		DropdownMenuTrigger
	} from '$lib/components/ui/dropdown-menu';

	let current = 'Select project';
	const options = ['ACME Fiber North', 'Berlin FTTH Lot 3', 'Munich Cabinets'];
</script>

<DropdownMenu>
	<DropdownMenuTrigger class={`${buttonVariants({ variant: 'ghost' })} gap-2`}>
		<Briefcase class="size-4 text-muted-foreground" />
		{current}
		<ChevronDown class="size-4 text-muted-foreground" />
	</DropdownMenuTrigger>
	<DropdownMenuContent align="start">
		{#each options as opt}
			<DropdownMenuItem textValue={opt}>
				<button class="w-full text-left" on:click={() => (current = opt)}>{opt}</button>
			</DropdownMenuItem>
		{/each}
	</DropdownMenuContent>
</DropdownMenu>
