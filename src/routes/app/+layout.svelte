<script lang="ts">
	import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from '$lib/components/ui/sheet';
	import { Button, buttonVariants } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import {
		DropdownMenu,
		DropdownMenuContent,
		DropdownMenuItem,
		DropdownMenuTrigger
	} from '$lib/components/ui/dropdown-menu';
	import Sidebar from '$lib/components/app/sidebar.svelte';
	import ProjectSwitcher from '$lib/components/app/project-switcher.svelte';
	import { Menu, Search, Bell, ChevronDown } from '@lucide/svelte';

	let { children } = $props();
</script>

<div class="min-h-dvh bg-background text-foreground">
	<header
		class="sticky top-0 z-50 border-b border-border bg-background/80 backdrop-blur supports-[backdrop-filter]:bg-background/60"
	>
		<div class="mx-auto flex h-14 max-w-screen-2xl items-center gap-2 px-4">
			<Sheet>
				<SheetTrigger class="lg:hidden" aria-label="Open navigation"
					><Menu class="size-5" /></SheetTrigger
				>
				<SheetContent side="left" class="w-72 p-0">
					<Sidebar />
				</SheetContent>
			</Sheet>

			<a href="/app" class="font-semibold">Vectrical</a>

			<div class="ms-2 hidden lg:flex">
				<ProjectSwitcher />
			</div>

			<div class="ms-auto flex items-center gap-2">
				<div class="hidden items-center gap-2 md:flex">
					<Search class="size-4 text-muted-foreground" />
					<Input placeholder="Search projects, objects, orders…" class="w-72" />
				</div>

				<Button variant="ghost" size="icon" aria-label="Notifications">
					<Bell class="size-5" />
				</Button>

				<DropdownMenu>
					<DropdownMenuTrigger class={`${buttonVariants({ variant: 'outline' })} gap-2`}>
						Account <ChevronDown class="size-4 text-muted-foreground" />
					</DropdownMenuTrigger>
					<DropdownMenuContent align="end">
						<DropdownMenuItem textValue="Dashboard"><a href="/app">Dashboard</a></DropdownMenuItem>
						<DropdownMenuItem textValue="Sign out"><a href="/">Sign out</a></DropdownMenuItem>
					</DropdownMenuContent>
				</DropdownMenu>
			</div>
		</div>
	</header>

	<div class="mx-auto grid max-w-screen-2xl grid-cols-1 lg:grid-cols-[280px_1fr]">
		<aside class="hidden border-r border-border lg:block">
			<Sidebar />
		</aside>
		<main class="min-w-0 p-4">
			{@render children?.()}
		</main>
	</div>
</div>
