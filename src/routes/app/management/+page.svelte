<script lang="ts">
	import { Card } from '$lib/components/ui/card';
	import { Input } from '$lib/components/ui/input';
	import { buttonVariants } from '$lib/components/ui/button';
	import {
		DropdownMenu,
		DropdownMenuContent,
		DropdownMenuItem,
		DropdownMenuTrigger
	} from '$lib/components/ui/dropdown-menu';
	import { onMount } from 'svelte';

	type Color = 'slate' | 'blue' | 'red' | 'green' | 'amber' | 'violet';
	type StatusEntry = { key: string; label: string; color: Color; order?: number };
	let local: StatusEntry[] = [];
	const colors: Color[] = ['slate', 'blue', 'red', 'green', 'amber', 'violet'];
	let loading = true;
	let saving = false;

	async function loadStatuses() {
		loading = true;
		try {
			const res = await fetch('/api/statuses');
			if (!res.ok) throw new Error('Failed to load');
			local = await res.json();
		} finally {
			loading = false;
		}
	}

	async function save() {
		saving = true;
		try {
			const res = await fetch('/api/statuses', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ statuses: local })
			});
			if (!res.ok) throw new Error('Save failed');
			local = await res.json();
		} finally {
			saving = false;
		}
	}

	onMount(loadStatuses);
</script>

<div class="space-y-4">
	<h1 class="text-xl font-semibold">Project Management</h1>

	<Card class="space-y-3 p-4">
		<div class="flex items-center justify-between">
			<h2 class="font-medium">Statuses</h2>
			<button
				type="button"
				class={buttonVariants({ variant: 'default' })}
				on:click={() => save()}
				disabled={saving}
			>
				{#if saving}Saving…{:else}Save{/if}
			</button>
		</div>
		{#if loading}
			<div class="text-sm text-muted-foreground">Loading…</div>
		{:else}
			{#each local as s, i}
				<div class="grid grid-cols-1 gap-2 sm:grid-cols-[1fr_auto] sm:items-center">
					<div class="grid grid-cols-1 gap-2 sm:grid-cols-2">
						<div class="space-y-1">
							<div class="text-xs text-muted-foreground">Key</div>
							<Input value={s.key} disabled />
						</div>
						<div class="space-y-1">
							<div class="text-xs text-muted-foreground">Label</div>
							<Input bind:value={local[i].label} />
						</div>
					</div>
					<div class="flex justify-start sm:justify-end">
						<DropdownMenu>
							<DropdownMenuTrigger
								class="inline-flex h-9 items-center justify-center rounded-md border px-4 text-sm"
							>
								Color: {s.color}
							</DropdownMenuTrigger>
							<DropdownMenuContent align="end">
								{#each colors as c}
									<DropdownMenuItem textValue={c}>
										<button class="w-full text-left" on:click={() => (local[i].color = c)}
											>{c}</button
										>
									</DropdownMenuItem>
								{/each}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
				</div>
			{/each}
		{/if}
		<p class="text-xs text-muted-foreground">
			Note: Requires DB migration to persist if table is new.
		</p>
	</Card>
</div>
