import type { PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { workOrder, object, status as statusTable } from '$lib/server/db/schema';
import { ensureSeed } from '$lib/server/db/seed';
import { eq } from 'drizzle-orm';

export const load: PageServerLoad = async ({ params }) => {
	await ensureSeed();
	const wo =
		(await db.select().from(workOrder).where(eq(workOrder.id, params.id)).limit(1))[0] || null;
	const obj = wo
		? (await db.select().from(object).where(eq(object.id, wo.objectId)).limit(1))[0] || null
		: null;
	const statuses = await db
		.select()
		.from(statusTable)
		.catch(() => [] as any);
	return { workOrder: wo, object: obj, statuses };
};
