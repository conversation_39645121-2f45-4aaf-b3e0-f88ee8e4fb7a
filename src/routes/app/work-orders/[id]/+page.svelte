<script lang="ts">
	import { Badge } from '$lib/components/ui/badge';
	function statusVariant(key: string): 'default' | 'secondary' | 'destructive' | 'outline' {
		if (key === 'blocked') return 'destructive';
		if (key === 'planned') return 'secondary';
		if (key === 'done') return 'outline';
		return 'default';
	}
	type Status = { key: string; label: string };
	type Obj = { id: string; name: string };
	type WorkOrder = {
		id: string;
		title: string;
		objectId: string;
		status: string;
		assignee?: string;
	};
	let { data } = $props<{ data: { workOrder: WorkOrder; object: Obj; statuses: Status[] } }>();
	const wo = data.workOrder;
	const obj = data.object;
	const statusMap = new Map<string, Status>(
		(data.statuses?.map((s: Status) => [s.key, s]) as [string, Status][]) ?? []
	);
	const labelFor = (k: string) => statusMap.get(k)?.label ?? k.replace('_', ' ');
</script>

{#if !wo}
	<p class="text-sm text-muted-foreground">Work order not found.</p>
{:else}
	<div class="space-y-2">
		<h1 class="text-xl font-semibold">{wo.title}</h1>
		<div class="flex items-center gap-2 text-sm">
			<Badge variant={statusVariant(wo.status)}>{labelFor(wo.status)}</Badge>
			<span
				>Object: <a href={`/app/objects/${obj?.id}`} class="hover:underline">{obj?.name}</a></span
			>
			<span>Assignee: {wo.assignee ?? '—'}</span>
		</div>
	</div>
{/if}
