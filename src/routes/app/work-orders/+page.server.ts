import type { PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { workOrder, object, status as statusTable } from '$lib/server/db/schema';
import { ensureSeed } from '$lib/server/db/seed';

export const load: PageServerLoad = async () => {
	await ensureSeed();
	const orders = await db.select().from(workOrder);
	const objs = await db.select().from(object);
	const statuses = await db
		.select()
		.from(statusTable)
		.catch(() => [] as any);
	return { workOrders: orders, objects: objs, statuses };
};
