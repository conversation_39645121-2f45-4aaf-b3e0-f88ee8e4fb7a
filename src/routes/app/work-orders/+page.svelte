<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow
	} from '$lib/components/ui/table';
	type Status = { key: string; label: string };
	type Obj = { id: string; name: string };
	type WorkOrder = { id: string; title: string; objectId: string; status: string };
	let { data } = $props<{
		data: { workOrders: WorkOrder[]; objects: Obj[]; statuses: Status[] };
	}>();
	const statusMap = new Map<string, Status>(
		(data.statuses?.map((s: Status) => [s.key, s]) as [string, Status][]) ?? []
	);
	const objs = new Map<string, Obj>(
		(data.objects?.map((o: Obj) => [o.id, o]) as [string, Obj][]) ?? []
	);
	const labelFor = (k: string) => statusMap.get(k)?.label ?? k.replace('_', ' ');
</script>

<div class="space-y-4">
	<div class="flex items-center justify-between">
		<h1 class="text-xl font-semibold">Work Orders</h1>
		<Button>New Work Order</Button>
	</div>
	<Table>
		<TableHeader>
			<TableRow>
				<TableHead>Title</TableHead>
				<TableHead>Status</TableHead>
				<TableHead>Object</TableHead>
			</TableRow>
		</TableHeader>
		<TableBody>
			{#each data.workOrders as w}
				{@const o = objs.get(w.objectId)}
				<TableRow>
					<TableCell
						><a class="hover:underline" href={`/app/work-orders/${w.id}`}>{w.title}</a></TableCell
					>
					<TableCell>{labelFor(w.status)}</TableCell>
					<TableCell
						><a class="hover:underline" href={`/app/objects/${o?.id}`}>{o?.name}</a></TableCell
					>
				</TableRow>
			{/each}
		</TableBody>
	</Table>
</div>
