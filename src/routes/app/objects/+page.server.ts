import type { PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { object, status as statusTable } from '$lib/server/db/schema';
import { ensureSeed } from '$lib/server/db/seed';

export const load: PageServerLoad = async () => {
	await ensureSeed();
	const rows = await db.select().from(object);
	const statuses = await db
		.select()
		.from(statusTable)
		.catch(() => [] as any);
	return { objects: rows, statuses };
};
