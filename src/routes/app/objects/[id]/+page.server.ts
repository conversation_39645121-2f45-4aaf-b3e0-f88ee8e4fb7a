import type { PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { object, workOrder, status as statusTable } from '$lib/server/db/schema';
import { ensureSeed } from '$lib/server/db/seed';
import { eq } from 'drizzle-orm';

export const load: PageServerLoad = async ({ params }) => {
	await ensureSeed();
	const obj = (await db.select().from(object).where(eq(object.id, params.id)).limit(1))[0] || null;
	const orders = await db.select().from(workOrder).where(eq(workOrder.objectId, params.id));
	const statuses = await db
		.select()
		.from(statusTable)
		.catch(() => [] as any);
	return { object: obj, orders, statuses };
};
