<script lang="ts">
	import { Card } from '$lib/components/ui/card';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow
	} from '$lib/components/ui/table';
	import { Badge } from '$lib/components/ui/badge';
	function statusVariant(key: string): 'default' | 'secondary' | 'destructive' | 'outline' {
		if (key === 'blocked') return 'destructive';
		if (key === 'planned') return 'secondary';
		if (key === 'done') return 'outline';
		return 'default';
	}
	type Status = { key: string; label: string };
	type WorkOrder = { id: string; title: string; status: string; assignee?: string };
	type Obj = { id: string; name: string; status: string; type: string; address?: string };
	let { data } = $props<{ data: { object: Obj; orders: WorkOrder[]; statuses: Status[] } }>();
	const obj = data.object;
	const recent = data.orders;
	const statusMap = new Map<string, Status>(
		(data.statuses?.map((s: Status) => [s.key, s]) as [string, Status][]) ?? []
	);
	const labelFor = (k: string) => statusMap.get(k)?.label ?? k.replace('_', ' ');
</script>

{#if !obj}
	<p class="text-sm text-muted-foreground">Object not found.</p>
{:else}
	<div class="space-y-4">
		<div class="flex items-center gap-2">
			<h1 class="text-xl font-semibold">{obj.name}</h1>
			<Badge variant={statusVariant(obj.status)}>{labelFor(obj.status)}</Badge>
			<span class="text-sm text-muted-foreground">{obj.type}</span>
		</div>

		<Card class="p-3">
			<div class="text-sm text-muted-foreground">Address</div>
			<div>{obj.address ?? '—'}</div>
		</Card>

		<div>
			<h2 class="mb-2 text-sm font-medium">Recent work orders</h2>
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>Title</TableHead>
						<TableHead>Status</TableHead>
						<TableHead>Assignee</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{#each recent as w}
						<TableRow>
							<TableCell
								><a class="hover:underline" href={`/app/work-orders/${w.id}`}>{w.title}</a
								></TableCell
							>
							<TableCell
								><Badge variant={statusVariant(w.status)}>{labelFor(w.status)}</Badge></TableCell
							>
							<TableCell>{w.assignee ?? '—'}</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</Table>
		</div>
	</div>
{/if}
