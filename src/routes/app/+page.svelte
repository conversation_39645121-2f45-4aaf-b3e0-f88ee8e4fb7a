<script lang="ts">
	import { onMount } from 'svelte';
	import { PUBLIC_CLERK_PUBLISHABLE_KEY } from '$env/static/public';
	import { goto } from '$app/navigation';

	let userButtonEl: HTMLDivElement;

	onMount(async () => {
		// Dynamically import to avoid SSR/CommonJS interop issues in Vite
		type ClerkCtor = new (key: string) => {
			load: (opts?: Record<string, unknown>) => Promise<void>;
			user?: unknown;
			mountUserButton: (el: Element, opts: Record<string, unknown>) => void;
		};
		type ClerkModule = {
			Clerk?: ClerkCtor;
			default?: { Clerk?: ClerkCtor };
		};
		const mod = (await import('@clerk/clerk-js')) as unknown as ClerkModule;
		const ClerkClass = (mod.Clerk ?? mod.default?.Clerk) as ClerkCtor | undefined;
		if (!ClerkClass) {
			console.error('Clerk constructor not found in @clerk/clerk-js exports', mod);
			return;
		}

		const clerk = new ClerkClass(PUBLIC_CLERK_PUBLISHABLE_KEY);

		// Temporarily silence SvelteKit history warnings during Clerk load
		const origWarn: (...args: unknown[]) => void = console.warn;
		console.warn = (...args: unknown[]) => {
			if (typeof args[0] === 'string' && args[0].includes('Avoid using `history.pushState')) {
				return;
			}
			origWarn(...(args as unknown[]));
		};

		try {
			await clerk.load({
				// Prefer SvelteKit routing
				routerPush: (to: string) => goto(to, { replaceState: false }),
				routerReplace: (to: string) => goto(to, { replaceState: true }),
				// Keep standard browser assumptions
				standardBrowser: true,
				// Modern redirect prop
				fallbackRedirectUrl: '/app'
			});
		} finally {
			console.warn = origWarn;
		}

		if (!clerk.user) {
			goto('/');
			return;
		}
		clerk.mountUserButton(userButtonEl, {});
	});
</script>

<div class="space-y-4 p-6">
	<div class="flex justify-end">
		<div bind:this={userButtonEl}></div>
	</div>
	<h1 class="text-2xl font-semibold">Welcome</h1>
	<p>You’re signed in via Clerk.</p>
</div>
