import type { PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { project, status as statusTable } from '$lib/server/db/schema';
import { ensureSeed } from '$lib/server/db/seed';

async function seedIfEmpty() {
	await ensureSeed();
}

export const load: PageServerLoad = async () => {
	await seedIfEmpty();
	const rows = await db.select().from(project);
	const statuses = await db
		.select()
		.from(statusTable)
		.catch(() => [] as any);
	return { projects: rows, statuses } as const;
};
