<script lang="ts">
	import { Badge } from '$lib/components/ui/badge';
	import { Button } from '$lib/components/ui/button';
	import {
		Table,
		TableBody,
		TableCaption,
		TableCell,
		TableHead,
		TableHeader,
		TableRow
	} from '$lib/components/ui/table';
	import { Card } from '$lib/components/ui/card';

	type Status = { key: string; label: string; color: string };
	type Project = {
		id: string;
		name: string;
		city: string;
		status: string;
		progress: number;
		objectsCount?: number;
		workOrdersCount?: number;
	};
	let { data } = $props<{ data: { projects: Project[]; statuses: Status[] } }>();
	const statusMap = new Map<string, Status>(
		(data.statuses?.map((s: Status) => [s.key, s]) as [string, Status][]) ?? []
	);
	function labelFor(key: string) {
		return statusMap.get(key)?.label ?? key.replace('_', ' ');
	}
	function statusVariant(key: string): 'default' | 'secondary' | 'destructive' | 'outline' {
		if (key === 'blocked') return 'destructive';
		if (key === 'planned') return 'secondary';
		if (key === 'done') return 'outline';
		return 'default';
	}
</script>

<div class="space-y-4">
	<div class="flex items-center justify-between">
		<h1 class="text-xl font-semibold">Projects</h1>
		<Button>New Project</Button>
	</div>

	<div class="hidden md:block">
		<Table>
			<TableHeader>
				<TableRow>
					<TableHead>Name</TableHead>
					<TableHead>City</TableHead>
					<TableHead>Status</TableHead>
					<TableHead class="text-right">Objects</TableHead>
					<TableHead class="text-right">Work Orders</TableHead>
					<TableHead class="text-right">Progress</TableHead>
				</TableRow>
			</TableHeader>
			<TableBody>
				{#each data.projects as p}
					<TableRow>
						<TableCell>
							<a class="hover:underline" href={`/app/projects/${p.id}`}>{p.name}</a>
						</TableCell>
						<TableCell>{p.city}</TableCell>
						<TableCell>
							<Badge variant={statusVariant(p.status)}>{labelFor(p.status)}</Badge>
						</TableCell>
						<TableCell class="text-right">{p.objects}</TableCell>
						<TableCell class="text-right">{p.workOrders}</TableCell>
						<TableCell class="text-right">{p.progress}%</TableCell>
					</TableRow>
				{/each}
			</TableBody>
			<TableCaption>FTTH construction projects</TableCaption>
		</Table>
	</div>

	<div class="grid gap-3 md:hidden">
		{#each data.projects as p}
			<Card class="p-3">
				<div class="flex items-start justify-between">
					<div>
						<a class="font-medium hover:underline" href={`/app/projects/${p.id}`}>{p.name}</a>
						<div class="text-sm text-muted-foreground">{p.city}</div>
					</div>
					<Badge variant={statusVariant(p.status)}>{labelFor(p.status)}</Badge>
				</div>
				<div class="mt-2 flex items-center justify-between text-sm">
					<div>Objects: {p.objects}</div>
					<div>Orders: {p.workOrders}</div>
					<div>{p.progress}%</div>
				</div>
			</Card>
		{/each}
	</div>
</div>
