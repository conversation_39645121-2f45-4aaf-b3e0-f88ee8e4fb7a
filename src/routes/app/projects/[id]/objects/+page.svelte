<script lang="ts">
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow
	} from '$lib/components/ui/table';
	import { Badge } from '$lib/components/ui/badge';
	type Status = { key: string; label: string };
	type Obj = { id: string; name: string; status: string; type: string };
	let { data } = $props<{ data: { objects: Obj[]; statuses: Status[] } }>();
	const list = data.objects;
	const statusMap = new Map<string, Status>(
		(data.statuses?.map((s: Status) => [s.key, s]) as [string, Status][]) ?? []
	);
	const labelFor = (k: string) => statusMap.get(k)?.label ?? k.replace('_', ' ');
	function statusVariant(key: string): 'default' | 'secondary' | 'destructive' | 'outline' {
		if (key === 'blocked') return 'destructive';
		if (key === 'planned') return 'secondary';
		if (key === 'done') return 'outline';
		return 'default';
	}
</script>

<div class="space-y-4">
	<h1 class="text-xl font-semibold">Project Objects</h1>
	<Table>
		<TableHeader>
			<TableRow>
				<TableHead>Name</TableHead>
				<TableHead>Type</TableHead>
				<TableHead>Status</TableHead>
			</TableRow>
		</TableHeader>
		<TableBody>
			{#each list as o}
				<TableRow>
					<TableCell
						><a class="hover:underline" href={`/app/objects/${o.id}`}>{o.name}</a></TableCell
					>
					<TableCell>{o.type}</TableCell>
					<TableCell
						><Badge variant={statusVariant(o.status)}>{labelFor(o.status)}</Badge></TableCell
					>
				</TableRow>
			{/each}
		</TableBody>
	</Table>
</div>
