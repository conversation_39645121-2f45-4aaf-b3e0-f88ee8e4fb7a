import type { PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { object, status as statusTable } from '$lib/server/db/schema';
import { ensureSeed } from '$lib/server/db/seed';
import { eq } from 'drizzle-orm';

export const load: PageServerLoad = async ({ params }) => {
	await ensureSeed();
	const list = await db.select().from(object).where(eq(object.projectId, params.id));
	const statuses = await db
		.select()
		.from(statusTable)
		.catch(() => [] as any);
	return { objects: list, statuses };
};
