import type { PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { project, object, workOrder, status as statusTable } from '$lib/server/db/schema';
import { ensureSeed } from '$lib/server/db/seed';
import { eq } from 'drizzle-orm';

export const load: PageServerLoad = async ({ params }) => {
	await ensureSeed();
	const p = (await db.select().from(project).where(eq(project.id, params.id)).limit(1))[0] || null;
	if (!p) return { project: null, objects: [], orders: [] };
	const objs = await db.select().from(object).where(eq(object.projectId, params.id));
	const orders = await db.select().from(workOrder);
	// filter orders by object ids client-side (workaround)
	const objIds = new Set(objs.map((o) => o.id));
	const filteredOrders = orders.filter((w) => objIds.has(w.objectId));
	const statuses = await db
		.select()
		.from(statusTable)
		.catch(() => [] as any);
	return { project: p, objects: objs, orders: filteredOrders, statuses };
};
