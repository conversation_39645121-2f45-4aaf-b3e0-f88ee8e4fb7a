<script lang="ts">
	import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from '$lib/components/ui/tabs';
	import { Badge } from '$lib/components/ui/badge';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow
	} from '$lib/components/ui/table';
	import { Card } from '$lib/components/ui/card';

	type Status = { key: string; label: string };
	type Obj = { id: string; name: string; status: string; type: string };
	type WorkOrder = { id: string; title: string; objectId: string; status: string };
	type Project = { id: string; name: string; city: string; status: string; progress: number };
	let { data } = $props<{
		data: { project: Project; objects: Obj[]; orders: WorkOrder[]; statuses: Status[] };
	}>();
	const project = data.project;
	const projectObjects = data.objects;
	const projectOrders = data.orders;
	const statusMap = new Map<string, Status>(
		(data.statuses?.map((s: Status) => [s.key, s]) as [string, Status][]) ?? []
	);
	const labelFor = (k: string) => statusMap.get(k)?.label ?? k.replace('_', ' ');
	function statusVariant(key: string): 'default' | 'secondary' | 'destructive' | 'outline' {
		if (key === 'blocked') return 'destructive';
		if (key === 'planned') return 'secondary';
		if (key === 'done') return 'outline';
		return 'default';
	}
</script>

{#if !project}
	<p class="text-sm text-muted-foreground">Project not found.</p>
{:else}
	<div class="space-y-4">
		<div class="flex flex-wrap items-center gap-2">
			<h1 class="text-xl font-semibold">{project.name}</h1>
			<Badge variant={statusVariant(project.status)}>{labelFor(project.status)}</Badge>
			<span class="text-sm text-muted-foreground">{project.city}</span>
		</div>

		<div class="grid grid-cols-1 gap-3 sm:grid-cols-3">
			<Card class="p-3">
				<div class="text-sm text-muted-foreground">Objects</div>
				<div class="text-2xl font-semibold">{projectObjects.length}</div>
			</Card>
			<Card class="p-3">
				<div class="text-sm text-muted-foreground">Work Orders</div>
				<div class="text-2xl font-semibold">{projectOrders.length}</div>
			</Card>
			<Card class="p-3">
				<div class="text-sm text-muted-foreground">Progress</div>
				<div class="text-2xl font-semibold">{project.progress}%</div>
			</Card>
		</div>

		<Tabs value="overview">
			<TabsList>
				<TabsTrigger value="overview">Overview</TabsTrigger>
				<TabsTrigger value="objects">Objects</TabsTrigger>
				<TabsTrigger value="orders">Work Orders</TabsTrigger>
			</TabsList>

			<TabsContent value="overview" class="space-y-2">
				<p class="text-sm text-muted-foreground">Project overview and recent activity.</p>
			</TabsContent>

			<TabsContent value="objects">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Name</TableHead>
							<TableHead>Type</TableHead>
							<TableHead>Status</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{#each projectObjects as o}
							<TableRow>
								<TableCell
									><a class="hover:underline" href={`/app/objects/${o.id}`}>{o.name}</a></TableCell
								>
								<TableCell>{o.type}</TableCell>
								<TableCell
									><Badge variant={statusVariant(o.status)}>{labelFor(o.status)}</Badge></TableCell
								>
							</TableRow>
						{/each}
					</TableBody>
				</Table>
			</TabsContent>

			<TabsContent value="orders">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Title</TableHead>
							<TableHead>Status</TableHead>
							<TableHead>Object</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{#each projectOrders as w}
							{@const o = projectObjects.find((x: Obj) => x.id === w.objectId)}
							<TableRow>
								<TableCell
									><a class="hover:underline" href={`/app/work-orders/${w.id}`}>{w.title}</a
									></TableCell
								>
								<TableCell
									><Badge variant={statusVariant(w.status)}>{labelFor(w.status)}</Badge></TableCell
								>
								<TableCell>{o?.name}</TableCell>
							</TableRow>
						{/each}
					</TableBody>
				</Table>
			</TabsContent>
		</Tabs>
	</div>
{/if}
