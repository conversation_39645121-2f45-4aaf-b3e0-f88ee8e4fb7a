<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { PUBLIC_CLERK_PUBLISHABLE_KEY } from '$env/static/public';

	let signInEl: HTMLDivElement;

	onMount(async () => {
		// Dev: if no publishable key, skip Clerk and go to /app
		if (!PUBLIC_CLERK_PUBLISHABLE_KEY) {
			goto('/app');
			return;
		}
		// Dynamically import to avoid SSR issues and handle CJS/ESM interop
		type ClerkCtor = new (key: string) => {
			load: () => Promise<void>;
			user?: unknown;
			mountSignIn: (
				el: Element,
				opts: { afterSignInUrl?: string; afterSignUpUrl?: string }
			) => void;
		};
		type ClerkModule = {
			Clerk?: ClerkCtor;
			default?: { Clerk?: ClerkCtor } | ClerkCtor;
		};

		const mod = (await import('@clerk/clerk-js')) as unknown as ClerkModule;
		const ClerkClass = (mod.Clerk ??
			(typeof mod.default === 'function' ? (mod.default as ClerkCtor) : mod.default?.Clerk)) as
			| ClerkCtor
			| undefined;

		if (!ClerkClass) return;

		const clerk = new ClerkClass(PUBLIC_CLERK_PUBLISHABLE_KEY);
		await clerk.load();

		// If already signed in, send them to the app
		if (clerk.user) {
			goto('/app');
			return;
		}

		// Otherwise, show Sign In and send to /app after auth
		clerk.mountSignIn(signInEl, {
			afterSignInUrl: '/app',
			afterSignUpUrl: '/app'
		});
	});
</script>

<!-- Start the app on the Clerk Sign In page -->
<div class="flex min-h-dvh items-center justify-center p-6">
	<div bind:this={signInEl}></div>
</div>
