import type { RequestHand<PERSON> } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { status as statusTable } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';

type Color = 'slate' | 'blue' | 'red' | 'green' | 'amber' | 'violet';
type StatusDTO = { key: string; label: string; color: Color; order?: number };

const isColor = (v: unknown): v is Color =>
	typeof v === 'string' && ['slate', 'blue', 'red', 'green', 'amber', 'violet'].includes(v);

const isStatus = (v: unknown): v is StatusDTO =>
	!!v &&
	typeof v === 'object' &&
	typeof (v as any).key === 'string' &&
	typeof (v as any).label === 'string' &&
	isColor((v as any).color) &&
	((v as any).order == null || Number.isInteger((v as any).order));

export const GET: RequestHandler = async ({ locals }) => {
	if (!locals.clerk?.session) return new Response('Unauthorized', { status: 401 });
	try {
		const rows = await db.select().from(statusTable).orderBy(statusTable.order, statusTable.key);
		if (!rows || rows.length === 0) {
			const defaults: StatusDTO[] = [
				{ key: 'planned', label: 'Planned', color: 'slate', order: 0 },
				{ key: 'in_progress', label: 'In progress', color: 'blue', order: 1 },
				{ key: 'blocked', label: 'Blocked', color: 'red', order: 2 },
				{ key: 'done', label: 'Done', color: 'green', order: 3 }
			];
			return Response.json(defaults, { headers: { 'x-status-persistent': 'maybe' } });
		}
		return Response.json(rows);
	} catch (err: any) {
		// Fallback: if table missing, return sensible defaults (non-persistent)
		if (String(err?.message || '').includes('no such table')) {
			const defaults: StatusDTO[] = [
				{ key: 'planned', label: 'Planned', color: 'slate', order: 0 },
				{ key: 'in_progress', label: 'In progress', color: 'blue', order: 1 },
				{ key: 'blocked', label: 'Blocked', color: 'red', order: 2 },
				{ key: 'done', label: 'Done', color: 'green', order: 3 }
			];
			return Response.json(defaults, { headers: { 'x-status-persistent': 'false' } });
		}
		return new Response('Server error', { status: 500 });
	}
};

// Upsert list of statuses (replace or insert by key)
export const POST: RequestHandler = async ({ request, locals }) => {
	if (!locals.clerk?.session) return new Response('Unauthorized', { status: 401 });
	const body = await request.json().catch(() => null);
	const list: unknown = (body as any)?.statuses;
	if (!Array.isArray(list) || !list.every(isStatus)) {
		return new Response('Invalid payload', { status: 400 });
	}
	try {
		for (const s of list as StatusDTO[]) {
			// Update first
			const updated = await db
				.update(statusTable)
				.set({ label: s.label, color: s.color, order: s.order ?? 0 })
				.where(eq(statusTable.key, s.key));
			const affected = (updated as unknown as { rowsAffected?: number }).rowsAffected ?? 0;
			if (!affected) {
				await db
					.insert(statusTable)
					.values({ key: s.key, label: s.label, color: s.color, order: s.order ?? 0 });
			}
		}
		// Return updated list
		const rows = await db.select().from(statusTable).orderBy(statusTable.order, statusTable.key);
		return Response.json(rows);
	} catch (err: any) {
		// If table doesn't exist yet, accept the payload for UX but mark non-persistent
		if (String(err?.message || '').includes('no such table')) {
			return Response.json(list as StatusDTO[], { headers: { 'x-status-persistent': 'false' } });
		}
		return new Response('Server error', { status: 500 });
	}
};
